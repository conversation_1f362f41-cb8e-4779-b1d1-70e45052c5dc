<script setup lang="ts">
import { computed } from 'vue'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import Icon from '@/components/Icon.vue'

interface Pendaftaran {
  id_pendaftaran: number
  nomor_pendaftaran: string
  status_pendaftaran: string
  golongan: {
    nama_golongan: string
    cabang_lomba: {
      nama_cabang: string
    }
  }
  pembayaran: {
    status_pembayaran: string
  } | null
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  status_peserta: string
  nik: string
  jenis_kelamin: string
  wilayah: {
    nama_wilayah: string
  }
}

interface Stats {
  total_pendaftaran: number
  pendaftaran_approved: number
  pendaftaran_pending: number
  dokumen_pending: number
}

const props = defineProps<{
  peserta: Peserta
  pendaftaran: Pendaftaran[]
  stats: Stats
}>()

const profileStatus = computed(() => {
  switch (props.peserta.status_peserta) {
    case 'draft': return { text: 'Draft', color: 'bg-gray-100 text-gray-800', icon: 'edit' }
    case 'submitted': return { text: 'Disubmit', color: 'bg-blue-100 text-blue-800', icon: 'clock' }
    case 'verified': return { text: 'Terverifikasi', color: 'bg-indigo-100 text-indigo-800', icon: 'check' }
    case 'approved': return { text: 'Disetujui', color: 'bg-green-100 text-green-800', icon: 'check-circle' }
    case 'rejected': return { text: 'Ditolak', color: 'bg-red-100 text-red-800', icon: 'x-circle' }
    default: return { text: props.peserta.status_peserta, color: 'bg-gray-100 text-gray-800', icon: 'help-circle' }
  }
})

const canRegister = computed(() => {
  return props.peserta.status_peserta === 'approved'
})

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

function getStatusColor(status: string): string {
  const colors = {
    draft: 'bg-gray-100 text-gray-800',
    submitted: 'bg-blue-100 text-blue-800',
    payment_pending: 'bg-yellow-100 text-yellow-800',
    paid: 'bg-green-100 text-green-800',
    verified: 'bg-indigo-100 text-indigo-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800'
  }
  return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
  const texts = {
    draft: 'Draft',
    submitted: 'Disubmit',
    payment_pending: 'Menunggu Pembayaran',
    paid: 'Sudah Dibayar',
    verified: 'Terverifikasi',
    approved: 'Disetujui',
    rejected: 'Ditolak'
  }
  return texts[status as keyof typeof texts] || status
}
</script>

<template>
  <AppLayout>
    <template #header>
      <Heading>Dashboard Peserta</Heading>
    </template>

    <Head title="Dashboard Peserta" />

    <div class="space-y-6">
      <!-- Welcome Message -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0 h-12 w-12">
              <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
                <span class="text-lg font-medium text-white">
                  {{ getInitials(peserta.nama_lengkap) }}
                </span>
              </div>
            </div>
            <div class="flex-1">
              <h2 class="text-xl font-semibold text-gray-900">
                Selamat datang, {{ peserta.nama_lengkap }}!
              </h2>
              <p class="text-gray-600">
                Kelola profil dan pendaftaran lomba MTQ Anda di sini.
              </p>
            </div>
            <div class="text-right">
              <Badge :class="profileStatus.color">
                <Icon :name="profileStatus.icon" class="w-3 h-3 mr-1" />
                {{ profileStatus.text }}
              </Badge>
              <p class="text-xs text-gray-500 mt-1">Status Profil</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Profile Status Alert -->
      <Alert v-if="!canRegister" class="border-yellow-200 bg-yellow-50">
        <Icon name="alert-triangle" class="h-4 w-4" />
        <AlertDescription>
          <div class="flex items-center justify-between">
            <span>
              Profil Anda belum disetujui. Silakan lengkapi data dan tunggu persetujuan admin untuk dapat mendaftar lomba.
            </span>
            <Button as="link" :href="route('settings.profile')" size="sm" variant="outline">
              Lengkapi Profil
            </Button>
          </div>
        </AlertDescription>
      </Alert>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="clipboard-list" class="h-8 w-8 text-blue-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.total_pendaftaran }}</p>
                <p class="text-sm text-gray-600">Total Pendaftaran</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="check-circle" class="h-8 w-8 text-green-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.pendaftaran_approved }}</p>
                <p class="text-sm text-gray-600">Disetujui</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="clock" class="h-8 w-8 text-yellow-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.pendaftaran_pending }}</p>
                <p class="text-sm text-gray-600">Menunggu</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <Icon name="file-text" class="h-8 w-8 text-orange-600" />
              <div>
                <p class="text-2xl font-bold">{{ stats.dokumen_pending }}</p>
                <p class="text-sm text-gray-600">Dokumen Pending</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="user" class="h-8 w-8 text-blue-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Profil Saya</h3>
                <p class="text-sm text-gray-500">Kelola data pribadi</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as="link" :href="route('settings.profile')" variant="outline" size="sm" class="w-full">
                Kelola Profil
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="clipboard-list" class="h-8 w-8 text-green-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Pendaftaran Saya</h3>
                <p class="text-sm text-gray-500">Lihat status pendaftaran</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as="link" :href="route('peserta.pendaftaran.index')" variant="outline" size="sm" class="w-full">
                Lihat Pendaftaran
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card class="hover:shadow-lg transition-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <Icon name="search" class="h-8 w-8 text-purple-600" />
              </div>
              <div class="flex-1">
                <h3 class="text-lg font-medium text-gray-900">Jelajahi Lomba</h3>
                <p class="text-sm text-gray-500">Cari cabang lomba</p>
              </div>
            </div>
            <div class="mt-4">
              <Button as="link" :href="route('competition.index')" variant="outline" size="sm" class="w-full">
                Jelajahi Lomba
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Recent Registrations -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Pendaftaran Terbaru</CardTitle>
            <Button as="link" :href="route('peserta.pendaftaran.index')" variant="outline" size="sm">
              Lihat Semua
            </Button>
          </div>
          <CardDescription>Pendaftaran lomba terbaru Anda</CardDescription>
        </CardHeader>
        <CardContent>
          <div v-if="pendaftaran.length === 0" class="text-center py-8">
            <Icon name="clipboard-list" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 mb-2">Belum Ada Pendaftaran</h3>
            <p class="text-gray-600 mb-4">Anda belum mendaftar untuk lomba apapun.</p>
            <Button
              v-if="canRegister"
              as="link"
              :href="route('peserta.pendaftaran.create')"
            >
              Daftar Lomba Pertama
            </Button>
            <Button
              v-else
              as="link"
              :href="route('settings.profile')"
              variant="outline"
            >
              Lengkapi Profil Dulu
            </Button>
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="reg in pendaftaran"
              :key="reg.id_pendaftaran"
              class="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div class="flex-1">
                <h4 class="font-medium">{{ reg.golongan.nama_golongan }}</h4>
                <p class="text-sm text-gray-600">{{ reg.golongan.cabang_lomba.nama_cabang }}</p>
                <p class="text-xs text-gray-500 mt-1">{{ reg.nomor_pendaftaran }}</p>
              </div>
              <div class="text-right">
                <Badge :class="getStatusColor(reg.status_pendaftaran)">
                  {{ getStatusText(reg.status_pendaftaran) }}
                </Badge>
                <div v-if="reg.pembayaran" class="mt-1">
                  <Badge
                    :class="reg.pembayaran.status_pembayaran === 'paid' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'"
                    variant="outline"
                    class="text-xs"
                  >
                    {{ reg.pembayaran.status_pembayaran === 'paid' ? 'Lunas' : 'Pending' }}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </AppLayout>
</template>
