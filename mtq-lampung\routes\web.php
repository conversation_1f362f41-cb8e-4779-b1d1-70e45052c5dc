<?php

use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\PesertaController;
use App\Http\Controllers\Admin\PendaftaranController;
use App\Http\Controllers\AdminDaerah\DashboardController as AdminDaerahDashboardController;
use App\Http\Controllers\AdminDaerah\PesertaController as AdminDaerahPesertaController;
use App\Http\Controllers\CompetitionController;
use App\Http\Controllers\Peserta\DashboardController as PesertaDashboardController;
use App\Http\Controllers\Peserta\PendaftaranController as PesertaPendaftaranController;
use App\Http\Controllers\Peserta\DokumenController;
use App\Http\Controllers\RedirectController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('Welcome');
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Role-based redirect
Route::get('redirect', [RedirectController::class, 'redirect'])
    ->middleware(['auth', 'verified'])
    ->name('redirect');

// Competition Routes (Public)
Route::prefix('competition')->name('competition.')->group(function () {
    Route::get('/', [CompetitionController::class, 'index'])->name('index');
    Route::get('/cabang/{id}', [CompetitionController::class, 'show'])->name('show');
    Route::get('/golongan/{id}', [CompetitionController::class, 'golongan'])->name('golongan');
});

// Admin Routes
Route::middleware(['auth', 'role:superadmin,admin,admin_daerah'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::resource('peserta', PesertaController::class);
    Route::resource('pendaftaran', PendaftaranController::class);
});

// Admin Daerah Routes
Route::middleware(['auth', 'role:admin_daerah'])->prefix('admin-daerah')->name('admin-daerah.')->group(function () {
    Route::get('/dashboard', [AdminDaerahDashboardController::class, 'index'])->name('dashboard');

    Route::resource('peserta', AdminDaerahPesertaController::class);
});

// Peserta Routes
Route::middleware(['auth', 'role:peserta'])->prefix('peserta')->name('peserta.')->group(function () {
    Route::get('/dashboard', [PesertaDashboardController::class, 'index'])->name('dashboard');
    Route::get('settings/profile', function() {
        return "Halaman Profile";
    });

    // Pendaftaran routes
    Route::resource('pendaftaran', PesertaPendaftaranController::class);

    // Document routes
    Route::prefix('pendaftaran/{pendaftaran}/dokumen')->name('dokumen.')->group(function () {
        Route::get('/', [DokumenController::class, 'index'])->name('index');
        Route::post('/', [DokumenController::class, 'store'])->name('store');
        Route::get('/{dokumen}/download', [DokumenController::class, 'download'])->name('download');
        Route::post('/{dokumen}/replace', [DokumenController::class, 'replace'])->name('replace');
        Route::delete('/{dokumen}', [DokumenController::class, 'destroy'])->name('destroy');
    });
});

// Redirect authenticated users based on role
Route::middleware(['auth'])->get('/redirect', function () {
    $user = Auth::user();

    switch ($user->role) {
        case 'superadmin':
        case 'admin':
        case 'admin_daerah':
            return redirect()->route('admin.dashboard');
        case 'peserta':
            return redirect()->route('peserta.dashboard');
        case 'dewan_hakim':
            return redirect()->route('dashboard'); // Default dashboard for now
        default:
            return redirect()->route('dashboard');
    }
})->name('redirect');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
